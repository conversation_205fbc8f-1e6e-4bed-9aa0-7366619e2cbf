/**
 * Security utility functions for preventing NoSQL injection attacks
  - . → \.
  - * → \*
  - + → \+
  - ? → \?
  - ^ → \^
  - $ → \$
  - { and } → \{ and \}
  - ( and ) → \( and \)
  - | → \|
  - [ and ] → \[ and \]
  - \ → \\
 */
import { QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
export const escapeRegexSpecialChars = (input: string): string => {
    return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

/**
 * Sanitizer class for preventing NoSQL injection attacks
 * Provides methods to escape regex special characters in user input before database queries
 * Main goal: Make user input safe for MongoDB regex operations while preserving data integrity
 */
export class Sanitizer {
    private static readonly UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[47][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    private static readonly PHONE_REGEX = /^(08|09|06)\d{8}$|^(02|03|04|05|07)\d{7}$/;
    private static readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    public static validateSanitization(type: string, input: string): boolean {
        if (type === "uuid") return this.UUID_REGEX.test(input);
        if (type === "phone") return this.PHONE_REGEX.test(input);
        if (type === "email") return this.EMAIL_REGEX.test(input);
        if (type === "text") return input.trim().length > 0;
        return false;
    }

    public static sanitizeUUID(input: string): string {
        // Clean input
        const cleaned = input.trim().toLowerCase();

        // Remove any potentially dangerous characters
        const sanitized = cleaned.replace(/[^0-9a-f-]/g, "");

        // Validate UUID format using regex
        if (!this.validateSanitization("uuid", sanitized)) {
            throw new QueryError(m.INVALID_ID, d.INVALID_ID);
        }

        return sanitized;
    }

    /**
     * Sanitizes phone number input by escaping regex special characters for NoSQL injection prevention
     * Escapes: . * + ? ^ $ {} () | [] \ (all regex special characters)
     * Preserves: Phone format including digits, hyphens, spaces, parentheses
     * @param input - Phone number to sanitize
     * @returns Sanitized phone number safe for regex queries
     */
    public static sanitizePhoneNumber(input: string): string {
        const cleaned = input.trim();
        const sanitized = cleaned.replace(/[^0-9]/g, "");

        // Validate phone number format using regex
        if (!this.validateSanitization("phone", sanitized)) {
            throw new QueryError(m.INVALID_PHONE, d.INVALID_PHONE);
        }

        return sanitized;
    }

    /**
     * Sanitizes email input by escaping regex special characters for NoSQL injection prevention
     * Escapes: . * + ? ^ $ {} () | [] \ (all regex special characters)
     * Preserves: @ _ - (safe email characters)
     * @param input - Email to sanitize
     * @returns Sanitized email safe for regex queries
     */
    public static sanitizeEmail(input: string): string {
        const cleaned = input
            .trim()
            .toLowerCase()
            .replace(/[^a-zA-Z0-9@._-]/g, "");

        if (!this.validateSanitization("email", cleaned)) {
            throw new QueryError(m.INVALID_EMAIL, d.INVALID_EMAIL);
        }

        const sanitize = cleaned.replace(/\./g, "\\.");

        return sanitize;
    }

    /**
     * Sanitizes text for search queries
     * @param input - Text to sanitize
     * @returns Sanitized text safe for search operations
     */
    public static sanitizeForSearch(input: string): string {
        const cleaned = input.trim();

        const sanitized = cleaned.replace(/[^a-zA-Z\s'-]/g, "");

        if (sanitized.length === 0 || sanitized.length > 255) {
            throw new QueryError(m.INVALID_SEARCH_QUERY, d.INVALID_SEARCH_QUERY);
        }
        const escape = sanitized.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        return escape;
    }
}
