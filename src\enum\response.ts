export enum ResponseTitleEnum {
    INFO = "Info",
    SUCCESS = "Success",
    WARNING = "Warning",
    ERROR = "Error",
    VALIDATION_ERROR = "Validation Error",
    UNAUTHENTICATED_ERROR = "Unauthenticated Error",
    FORBIDDEN_ERROR = "Forbidden Error",
    NOT_FOUND_ERROR = "Not Found Error",
    INTERNAL_SERVER_ERROR = "Internal Server Error",
    RATE_LIMIT_ERROR = "Rate Limit Error",
    DATABASE_ERROR = "Database Error",
    MODULE_ERROR = "Module Error",
    REPOSITORY_ERROR = "Repository Error",
    ENTITY_ERROR = "Entity Error",
    SERVICE_ERROR = "Service Error",
    UNEXPECTED_ERROR = "Unexpected error",
}
export enum ResponseStatusCodeEnum {
    // Success Responses (2xx)
    SUCCESS = 200,

    // Redirection (3xx)
    MOVED_PERMANENTLY = 301,
    FOUND = 302,
    NOT_MODIFIED = 304,

    // Client Errors (4xx)
    BAD_REQUEST = 400,
    UNAUTHENTICATED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405,
    CONFLICT = 409,
    UNPROCESSABLE_ENTITY = 422,
    TOO_MANY_REQUESTS = 429,

    // Server Errors (5xx)
    INTERNAL_SERVER_ERROR = 500,
    NOT_IMPLEMENTED = 501,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504,
}
export enum ResponseMessageEnum {
    // Success Messages
    SUCCESS = "Operation completed successfully.",
    CREATED = "Resource created successfully.",
    UPDATED = "Resource updated successfully.",
    DELETED = "Resource deleted successfully.",
    ACCEPTED = "Request has been accepted for processing.",
    NO_CONTENT = "No content available.",

    // Validation Messages
    VALIDATION_ERROR = "Validation failed. Please check your input.",
    INVALID_INPUT = "The provided input is invalid.",
    MISSING_FIELDS = "Required fields are missing or incomplete, Ensure all required fields are provided.",
    INVALID_EMAIL = "The email format is incorrect.",
    INVALID_PHONE = "The phone number format is incorrect.",
    INVALID_SEARCH_QUERY = "The search query format is invalid.",
    INVALID_PASSWORD = "Password does not meet security requirements.",
    INVALID_ID = "The id format is incorrect.",
    INVALID_FORMAT = "The provided input is invalid format.",

    // RBAC
    // users
    GET_USER_ROLES_FAILED = "Failed to retrieve user roles.",
    GET_USER_PERMISSIONS_FAILED = "Failed to retrieve user permissions.",
    ADD_USER_PERMISSION_FAILED = "Failed to add user permission.",
    REMOVE_USER_PERMISSION_FAILED = "Failed to remove user permission.",

    //roles
    GET_ALL_ROLES_FAILED = "Failed to retrieve all roles data.",
    GET_ONE_ROLE_FAILED = "Failed to retrieve role data.",
    CREATE_ROLE_FAILED = "Failed to create role data.",
    UPDATE_ROLE_FAILED = "Failed to update role data.",
    DELETE_ROLE_FAILED = "Failed to delete role data.",

    //permissions
    GET_ALL_PERMISSIONS_FAILED = "Failed to retrieve all permissions data.",
    GET_ONE_PERMISSION_FAILED = "Failed to retrieve permission data.",
    GET_LIST_PERMISSIONS_FAILED = "Failed to retrieve list of permissions data.",
    CREATE_PERMISSION_FAILED = "Failed to create permission data.",
    UPDATE_PERMISSION_FAILED = "Failed to update permission data.",
    DELETE_PERMISSION_FAILED = "Failed to delete permission data.",

    // Duplicate data
    DUPLICATED_DATA = "This data already used, Please check your input data again.",
    DUPLICATED_NAME = "This name already used, Please check your input name again.",
    DUPLICATED_EMAIL = "This email already used, Please check your input email again.",
    DUPLICATED_REQUIREMENT_NAME = "This requirement name already used, Please check your input requirement name again.",
    DUPLICATED_HNID = "This HN Id already used, Please check your input HN Id again.",
    DUPLICATED_PHONE_NUMBER = "This phone number already used, Please check your input phone number again.",
    DUPLICATED_SPECIES_TYPE = "This species and type already used, Please check your input again.",
    DUPLICATED_RECIPE_NAME = "This recipe name already used, Please check your input recipe name again.",

    // Query Error
    ID_NOT_FOUND = "The provided id is not found, Please check your input id.",
    INVALID_EXERCISE_RANGE_FORMAT = "The proviced exercise range is invalid format.",
    INVALID_BCS_SCORE_FORMAT = "The proviced bcs scores is invalid format.",
    CALCULATE_BCS_SCORE_FAILED = "Failed to calculate bcs score.",
    MAPPING_NUTRIENTS_FAILED = "Failed to mapping nutrients.",

    // Authentication & Authorization
    UNAUTHENTICATED = "Unauthorized token error. You are not authorized to access this resource.",
    FORBIDDEN = "You do not have permission to perform this action.",
    INVALID_CREDENTIALS = "Invalid username or password.",
    ACCOUNT_LOCKED = "Your account is locked due to multiple failed attempts.",
    SESSION_EXPIRED = "Your session has expired. Please log in again.",
    KEY_NOT_FOUND = "Key not found for token verification. Ensure service Key not found for token verification. Ensure service properly configured.",
    INVALID_TOKEN = "Invalid token.",
    TOKEN_EXPIRED_ERROR = "Token has expired.",
    DECODE_TOKEN_ERROR = "Token decoding failed. Unexpected error occurred.",

    // Client Errors
    BAD_REQUEST = "The request was invalid or cannot be served.",
    NOT_FOUND = "The requested resource was not found.",
    METHOD_NOT_ALLOWED = "This HTTP method is not allowed on this endpoint.",
    CONFLICT = "The request conflicts with the current state of the resource.",
    TOO_MANY_REQUESTS = "Too many requests. Please try again later.",
    RESOURCE_EXISTS = "The resource already exists.",

    // File & Upload Messages
    FILE_UPLOAD_SUCCESS = "File uploaded successfully.",
    FILE_UPLOAD_ERROR = "Error occurred while uploading the file.",
    FILE_TOO_LARGE = "The file size exceeds the allowed limit.",
    INVALID_FILE_TYPE = "Unsupported file format.",

    // Rate Limiting & API Restrictions
    RATE_LIMIT_EXCEEDED = "You have exceeded the allowed number of requests.",
    API_KEY_INVALID = "The provided API key is invalid.",
    API_ACCESS_DENIED = "Access to this API is restricted.",

    // Server Errors
    INTERNAL_SERVER_ERROR = "An unexpected error occurred on the server.",
    SERVICE_UNAVAILABLE = "The service is currently unavailable. Please try again later.",
    GATEWAY_TIMEOUT = "The server did not receive a timely response.",
    DATABASE_ERROR = "A database error occurred.",
    EXTERNAL_SERVICE_ERROR = "An external service error occurred.",
    SERVICE_CONFIGURED_ERROR = "The service is not properly configured.",
    ENTITY_INITAILIZE_ERROR = "Failed to initialize entity.",
    INSERTION_FAILED = "Failed to insert data into the database.",

    //User
    CREDENTIALS_NOT_FOUND = "Credentials not found.",
    UPDATE_USER_SUCCESS = "User data updated successfully.",
    DELETE_USER_SUCCESS = "User data deleted successfully.",
    GET_ONE_USER_FAILED = "Failed to retrieve user data.",
    GET_ALL_USER_FAILED = "Failed to retrieve all users data.",
    GET_LIST_USER_FAILED = "Failed to retrieve list of users data.",
    CREATE_USER_FAILED = "Failed to create user data.",
    UPDATE_USER_FAILED = "Failed to update user data.",
    DELETE_USER_FAILED = "Failed to delete user data.",

    // Requirement
    GET_ONE_REQUIREMENT_FAILED = "Failed to retrieve requirement data.",
    GET_LIST_REQUIREMENT_WITH_FILTER_FAILED = "Failed to retrieve list of requirements with filter data.",
    GET_ALL_REQUIREMENT_FAILED = "Failed to retrieve all requirements data.",
    GET_ALL_REQUIREMENT_SPECIES_FAILED = "Failed to retrieve requirements by species data.",
    CREATE_MULTIPLE_REQUIREMENTS_FAILED = "Failed to create multiple requirements data.",
    CREATE_REQUIREMENT_FAILED = "Failed to create requirement data.",
    UPDATE_REQUIREMENT_FAILED = "Failed to update requirement data.",
    DELETE_REQUIREMENT_FAILED = "Failed to delete requirement data.",

    // Step
    SUBMIT_PET_INFO_STEP_FAILED = "Failed to submit pet information step.",
    SUBMIT_PET_HEALTH_STEP_FAILED = "Failed to submit pet health step.",
    SUBMIT_PET_REQUIREMENT_STEP_FAILED = "Failed to submit pet requirement step.",
    SUBMIT_PET_PERSONALIZE_STEP_FAILED = "Failed to submit pet personalize step.",
    SUBMIT_PET_PRODUCT_MATCH_STEP_FAILED = "Failed to submit pet product match step.",
    SUBMIT_PET_ORDER_SUMMARY_STRP_FAILED = "Failed to submit pet order summary step.",

    // Visit
    GET_ONE_VISIT_FAILED = "Failed to retrieve visit data.",
    GET_ALL_VISIT_FAILED = "Failed to retrieve all visits data.",
    GET_LIST_VISIT_FAILED = "Failed to retrieve list of visits data.",
    GET_VISIT_STATUS_COUNTS_FAILED = "Failed to retrieve visit status counts data.",
    GET_ALL_VISIT_DETAIL_FAILED = "Failed to retrieve visits details data.",
    CREATE_VISIT_FAILED = "Failed to create visit data.",
    CREATE_VISIT_FROM_PET_FAILED = "Failed to create visit from pet data.",
    UPDATE_VISIT_FAILED = "Failed to update visit data.",
    DELETE_VISIT_FAILED = "Failed to delete visit data.",

    // Breed
    GET_ONE_BREED_FAILED = "Failed to retrieve breed data.",
    GET_ALL_BREED_FAILED = "Failed to retrieve all breeds data.",
    GET_ALL_BREED_WITH_FILTER_FAILED = "Failed to retrieve filtered breeds data.",
    GET_LIST_BREED_FAILED = "Failed to retrieve list of breeds data.",
    CREATE_BREED_FAILED = "Failed to create breed data.",
    CREATE_MULTIPLE_BREED_FAILED = "Failed to create multiple breed data.",
    UPDATE_BREED_FAILED = "Failed to update breed data.",
    DELETE_BREED_FAILED = "Failed to delete breed data.",

    // Order
    GET_ONE_ORDER_FAILED = "Failed to retrieve order data.",
    GET_LIST_ORDER_FAILED = "Failed to retrieve list of orders data.",
    GET_ALL_ORDER_FAILED = "Failed to retrieve all orders data.",
    CREATE_ORDER_FAILED = "Failed to create order data.",
    UPDATE_ORDER_FAILED = "Failed to update order data.",
    DELETE_ORDER_FAILED = "Failed to delete order data.",

    // Owner
    GET_ONE_OWNER_FAILED = "Failed to retrieve owner data.",
    GET_ALL_OWNER_FAILED = "Failed to retrieve all owners data.",
    GET_ALL_OWNER_WITH_FILTER_FAILED = "Failed to retrieve filtered owners data.",
    GET_LIST_OWNER_FAILED = "Failed to retrieve list of owners data.",
    CREATE_OWNER_FAILED = "Failed to create owner data.",
    UPDATE_OWNER_FAILED = "Failed to update owner data.",
    DELETE_OWNER_FAILED = "Failed to delete owner data.",
    OWNER_ID_NOT_FOUND = "The provided owner id is not found, Please check your input owner id.",
    OWNER_ID_FIELD_REQUIRED = "The owner id field is required.",
    OWNER_INFO_AND_ID_CONFLICT = "Cannot provide both owner information and existing owner ID.",
    OWNER_INFO_OR_ID_REQUIRED = "Require owner information or existing owner ID.",

    // Pet
    GET_ONE_PET_FAILED = "Failed to retrieve pet data.",
    GET_ALL_PET_FAILED = "Failed to retrieve all pets data.",
    GET_LIST_PET_FAILED = "Failed to retrieve list of pets data.",
    CREATE_PET_FAILED = "Failed to create pet data.",
    CREATE_PET_WITH_OWNER_FAILED = "Failed to create pet with owner data.",
    UPDATE_PET_FAILED = "Failed to update pet data.",
    DELETE_PET_FAILED = "Failed to delete pet data.",
    OWNER_RESOLVE_FAILED = "Failed to resolve owner.",

    // Questionaire
    GET_ONE_QUESTIONAIRE_FAILED = "Failed to retrieve questionaire data.",
    GET_ALL_QUESTIONAIRE_FAILED = "Failed to retrieve all questionaires data.",
    GET_ALL_QUESTIONAIRE_SPECIES_FAILED = "Failed to retrieve questionaires by species data.",
    CREATE_QUESTIONAIRE_FAILED = "Failed to create questionaire data.",
    UPDATE_QUESTIONAIRE_FAILED = "Failed to update questionaire data.",
    DELETE_QUESTIONAIRE_FAILED = "Failed to delete questionaire data.",

    // Recipe
    GET_ONE_RECIPE_FAILED = "Failed to retrieve recipe data.",
    GET_LIST_RECIPE_FAILED = "Failed to retrieve list of recipes data.",
    GET_ALL_RECIPE_FAILED = "Failed to retrieve all recipes data.",
    CREATE_RECIPE_FAILED = "Failed to create recipe data.",
    CREATE_MULTIPLE_RECIPES_FAILED = "Failed to create multiple recipes data.",
    UPDATE_RECIPE_FAILED = "Failed to update recipe data.",
    DELETE_RECIPE_FAILED = "Failed to delete recipe data.",
    MATCHING_RECIPES_FAILED = "Failed to match recipes.",

    // Doctor
    GET_ALL_DOCTORS_FAILED = "Failed to retrieve all doctors data.",
    CREATE_DOCTOR_FAILED = "Failed to create doctor data.",
    UPDATE_DOCTOR_FAILED = "Failed to update doctor data.",
    DELETE_DOCTOR_FAILED = "Failed to delete doctor data.",
    // Disease
    GET_ALL_DISEASES_FAILED = "Failed to retrieve all diseases data.",
    CREATE_DISEASE_FAILED = "Failed to create disease data.",
    UPDATE_DISEASE_FAILED = "Failed to update disease data.",
    DELETE_DISEASE_FAILED = "Failed to delete disease data.",
    // Raw Mat
    GET_ALL_RAWMATS_FAILED = "Failed to retrieve all rawmats data.",
    CREATE_RAWMAT_FAILED = "Failed to create rawmat data.",
    UPDATE_RAWMAT_FAILED = "Failed to update rawmat data.",
    DELETE_RAWMAT_FAILED = "Failed to delete rawmat data.",
}
